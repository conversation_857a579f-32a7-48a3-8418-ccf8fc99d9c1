import bannerImg1 from "../../../../assets/images/factoryImg.jpg";
import { Check } from "lucide-react";

function TopCompressor() {
  return (
    <>
      <div className="bg-gray-100 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center lg:text-left mb-8">
            <p className="font-semibold text-sm sm:text-base lg:text-xl uppercase tracking-wide mb-3 sm:mb-4" style={{ color: 'var(--primary)' }}>
              Top Mining Equipment Manufacturer In China
            </p>
            <h1 className="text-lg text-black sm:text-2xl md:text-2xl lg:text-3xl font-bold py-4 leading-tight text-balance lg:text-pretty">
              One Stop Solution On Mining, Ore, Stone, Building Material Processing.
            </h1>
          </div>

          {/* Main Content Section */}
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 xl:gap-12 lg:items-stretch">
            {/* Image Section */}
            <div className="w-full lg:w-1/2">
              <img
                src={bannerImg1}
                alt="Factory manufacturing equipment"
                className="w-full h-64 sm:h-80 lg:h-full object-cover rounded-lg shadow-2xl"
              />
            </div>

            {/* Content Section */}
            <div className="w-full lg:w-1/2 bg-white rounded-xl shadow-xl p-6 sm:p-8 lg:p-12 flex flex-col justify-between">
              <div>
                <p className="text-gray-600 text-sm sm:text-base mb-6 leading-relaxed">
                  Sentai Machinery provides high quality and cost effective
                  crushers, sand making machines, rotary dryer, rotary kiln and
                  other customized mining machinery. Feel free to get a quote if you
                  need them.
                </p>
                
                <h2 className="text-lg sm:text-xl font-semibold text-gray-800 mb-6 leading-tight">
                  Providing Innovative Solution For Mining And Building Material
                  Processing.
                </h2>
              </div>
              
              <ul className="space-y-3 sm:space-y-4">
                <li className="flex items-start gap-3">
                  <Check 
                    className="w-5 h-5 text-white rounded-sm p-1 flex-shrink-0 mt-0.5" 
                    style={{ backgroundColor: 'var(--primary)' }}
                  />
                  <span className="text-gray-600 text-sm sm:text-base">
                    We Use Quality Manufacturing Materials
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <Check 
                    className="w-5 h-5 text-white rounded-sm p-1 flex-shrink-0 mt-0.5" 
                    style={{ backgroundColor: 'var(--primary)' }}
                  />
                  <span className="text-gray-600 text-sm sm:text-base">
                    STCrushers Provide Unique Technology
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <Check 
                    className="w-5 h-5 text-white rounded-sm p-1 flex-shrink-0 mt-0.5" 
                    style={{ backgroundColor: 'var(--primary)' }}
                  />
                  <span className="text-gray-600 text-sm sm:text-base">
                    Group Of Certified & Experienced Team
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <Check 
                    className="w-5 h-5 text-white rounded-sm p-1  flex-shrink-0 mt-0.5" 
                    style={{ backgroundColor: 'var(--primary)' }}
                  />
                  <span className="text-gray-600 text-sm sm:text-base">
                    The Best Services Of Multiple Industries
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default TopCompressor;
