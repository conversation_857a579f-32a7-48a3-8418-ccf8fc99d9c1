// src/pages/About.jsx
import product1Img from "../assets/images/factoryImg.jpg";

export default function AboutPage() {
  return (
    <div className="space-y-24 px-6 md:px-20 py-12">
      {/* Hero Section */}
      <section className="relative bg-cover bg-center h-[60vh] rounded-xl overflow-hidden flex items-center justify-center text-white" style={{ backgroundImage: `linear-gradient(rgba(28, 26, 48, 0.75), rgba(28, 26, 48, 0.75)), url(${product1Img})` }}>
        <div className="text-center max-w-2xl">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">About Our Company</h1>
          <p className="text-lg text-gray-200">Leading the future of industrial air solutions with innovation and integrity.</p>
        </div>
      </section>

      {/* Company Overview */}
      <section className="max-w-5xl mx-auto text-center space-y-6">
        <h2 className="text-3xl md:text-4xl font-bold text-[var(--primary)]">Who We Are</h2>
        <p className="text-gray-700 text-lg leading-relaxed">
          We are a trusted provider of industrial air compressors and custom manufacturing services, delivering high-performance solutions to businesses worldwide. With decades of engineering experience, our team designs equipment that’s reliable, efficient, and built for performance.
        </p>
      </section>

      {/* Mission, Vision, Values */}
      <section className="max-w-6xl mx-auto grid md:grid-cols-3 gap-8 text-center">
        <div className="bg-white shadow-md p-6 rounded-xl">
          <h3 className="text-xl font-semibold mb-2 text-[var(--primary)]">Our Mission</h3>
          <p className="text-gray-600">To empower industries with cutting-edge air solutions that improve productivity and reliability.</p>
        </div>
        <div className="bg-white shadow-md p-6 rounded-xl">
          <h3 className="text-xl font-semibold mb-2 text-[var(--primary)]">Our Vision</h3>
          <p className="text-gray-600">To be the global leader in industrial air compression technology through continuous innovation.</p>
        </div>
        <div className="bg-white shadow-md p-6 rounded-xl">
          <h3 className="text-xl font-semibold mb-2 text-[var(--primary)]">Our Values</h3>
          <p className="text-gray-600">Integrity, Excellence, Innovation, Customer-Centricity, and Sustainability.</p>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="max-w-6xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-10 text-[var(--primary)]">Why Choose Us</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {["20+ Years of Experience", "Custom Engineering Solutions", "ISO-Certified Manufacturing", "Global Shipping and Support"].map((item, i) => (
            <div key={i} className="bg-white shadow-md p-6 rounded-xl text-center border hover:shadow-lg transition duration-300">
              <p className="text-gray-800 font-medium">{item}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
