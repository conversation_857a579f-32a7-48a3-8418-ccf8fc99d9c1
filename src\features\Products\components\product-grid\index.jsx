// src/components/products/product-grid/ProductGrid.jsx
import { Package, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

export default function ProductGrid({ products }) {
  return (
    <div>
      {/* Results Header */}
      <div className="flex items-center justify-between mb-6">
        <p className="text-sm text-gray-600">
          Showing {products.length} {products.length === 1 ? 'product' : 'products'}
        </p>
     
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
        {products.map((product) => (
          <div
            key={product.id}
            className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden"
          >
            {/* Image */}
            <div className="overflow-hidden">
              <img
                src={product.image}
                alt={product.title}
                className="w-full h-52 object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>

            {/* Content */}
            <div className="p-5 space-y-2">
              <h3 className="text-xl font-semibold text-[#1c1a30] group-hover:text-[var(--primary)] transition-colors duration-200 min-h-[56px]">
                {product.title}
              </h3>
              <p className="text-sm text-gray-600 min-h-[60px]">
                {product.description}
              </p>
              
              {/* Price and Brand */}
              <div className="flex items-center justify-between mb-3">
                <span className="text-lg font-bold text-[var(--primary)]">
                  ${product.price}
                </span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {product.brand}
                </span>
              </div>
              
              {/* Tags */}
              <div className="flex flex-wrap gap-1 mb-3">
                {product.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <Link
                to={`/products/${product.id}`}
                className="inline-flex items-center gap-1 text-sm font-medium text-[var(--primary)] hover:underline transition-colors"
              >
                Read More
                <ArrowRight size={14} />
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {products.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Package size={64} className="mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-600">Try adjusting your filters to see more results.</p>
        </div>
      )}
    </div>
  );
}
