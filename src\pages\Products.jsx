// src/pages/Products.jsx
import { useState } from "react";
import { Filter, X } from "lucide-react";
import ProductHeader from "../features/Products/components/product-header";
import ProductFilter from "../features/Products/components/product-filter";
import ProductGrid from "../features/Products/components/product-grid";
import product1Img from "../assets/images/factoryImg.jpg";
import Footer from "../layout/footer";

const allProducts = [
  {
    id: 1,
    title: "Industrial Air Compressor",
    description: "High-performance compressor for large-scale operations.",
    image: product1Img,
    tags: ["Airpump"],
    price: 1200,
    capacity: "80 Gallon",
    power: "5 HP",
    weight: "350 lbs",
  },
  {
    id: 2,
    title: "Portable Air Compressor",
    description: "Compact and powerful solution for mobile use.",
    image: product1Img,
    tags: ["Airpump"],
    price: 450,
    capacity: "20 Gallon",
    power: "2 HP",
    weight: "75 lbs",
  },
  {
    id: 3,
    title: "Commercial Air Compressor",
    description: "Reliable compressor for shops and small businesses.",
    image: product1Img,
    tags: ["Airpump"],
    price: 800,
    capacity: "60 Gallon",
    power: "3 HP",
    weight: "220 lbs",
  },
  {
    id: 4,
    title: "Heavy-Duty Industrial Compressor",
    description: "Maximum power for industrial applications.",
    image: product1Img,
    tags: ["Airpump"],
    price: 1500,
    capacity: "120 Gallon",
    power: "7.5 HP",
    weight: "450 lbs",
  },
  {
    id: 5,
    title: "Replacement Filter",
    description: "High-quality replacement filter for air compressors.",
    image: product1Img,
    tags: ["Spare Parts"],
    price: 50,
    capacity: "N/A",
    power: "N/A",
    weight: "2 lbs",
  },
  {
    id: 6,
    title: "Pressure Gauge",
    description: "Accurate pressure gauge for monitoring.",
    image: product1Img,
    tags: ["Spare Parts"],
    price: 25,
    capacity: "N/A",
    power: "N/A",
    weight: "1 lb",
  },
  {
    id: 7,
    title: "Air Hose",
    description: "Durable air hose for various applications.",
    image: product1Img,
    tags: ["Spare Parts"],
    price: 75,
    capacity: "50 feet",
    power: "N/A",
    weight: "8.5 lbs",
  },
];

const filterOptions = {
  priceRanges: [
    { label: "All Prices", min: 0, max: Infinity },
    { label: "Under $100", min: 0, max: 99 },
    { label: "$100 - $500", min: 100, max: 500 },
    { label: "$500 - $1000", min: 500, max: 1000 },
    { label: "$1000+", min: 1000, max: Infinity },
  ],
};

export default function ProductsPage() {
  const [filters, setFilters] = useState({
    productType: "All",
    priceRange: "All Prices",
  });
  const [sortBy, setSortBy] = useState("featured");
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Apply filters
  const filteredProducts = allProducts.filter((product) => {
    // Product type filter
    const productTypeMatch =
      filters.productType === "All" ||
      product.tags.includes(filters.productType);

    // Price filter
    const selectedPriceRange = filterOptions.priceRanges.find(
      (range) => range.label === filters.priceRange
    );
    const priceMatch =
      product.price >= selectedPriceRange.min &&
      product.price <= selectedPriceRange.max;

    return productTypeMatch && priceMatch;
  });

  // Apply sorting
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price;
      case "price-high":
        return b.price - a.price;
      case "name":
        return a.title.localeCompare(b.title);
      case "newest":
        return b.id - a.id; // Assuming higher ID = newer
      default:
        return 0; // Featured - keep original order
    }
  });

  const handleFilterChange = (filterType, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }));
  };

  const closeMobileFilter = () => {
    setIsMobileFilterOpen(false);
  };

  const openMobileFilter = () => {
    setIsMobileFilterOpen(true);
  };

  return (
    <div>
      <ProductHeader />

      {/* Main Content */}
      <div className="px-6 md:px-20 py-12">
        {/* Mobile Filter Toggle */}
        <div className="lg:hidden mb-6">
          <button
            onClick={openMobileFilter}
            className="flex items-center gap-2 px-4 py-2 bg-[var(--primary)] text-white rounded-lg hover:bg-[var(--primary)]/90 transition-colors shadow-md"
          >
            <Filter size={20} />
            Filters
            {(filters.productType !== "All" ||
              filters.priceRange !== "All Prices") && (
              <span className="bg-white text-[var(--primary)] text-xs px-2 py-1 rounded-full font-medium ml-1">
                Active
              </span>
            )}
          </button>
        </div>

        {/* Main Layout */}
        <div className="flex gap-8">
          {/* Desktop Sidebar Filter */}
          <div className="hidden lg:block w-64 flex-shrink-0">
            <ProductFilter
              filterOptions={filterOptions}
              filters={filters}
              onFilterChange={handleFilterChange}
              isMobile={false}
            />
          </div>

          {/* Mobile Filter Overlay */}
          <div
            className={`lg:hidden fixed inset-0 z-50 transition-all duration-300 ease-in-out ${
              isMobileFilterOpen
                ? "opacity-100 pointer-events-auto"
                : "opacity-0 pointer-events-none"
            }`}
          >
            {/* Backdrop */}
            <div
              className={`absolute inset-0  transition-opacity duration-300 ${
                isMobileFilterOpen ? "bg-opacity-50" : "bg-opacity-0"
              }`}
              onClick={closeMobileFilter}
            />

            {/* Sidebar */}
            <div
              className={`relative bg-white w-80 max-w-[85vw] h-full shadow-2xl transform transition-transform duration-300 ease-out ${
                isMobileFilterOpen ? "translate-x-0" : "-translate-x-full"
              }`}
            >
              {/* Header */}
              <div className="flex justify-between items-center p-6 border-b bg-white sticky top-0 z-10 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
                <button
                  onClick={closeMobileFilter}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              {/* Filter Content */}
              <div className="p-6 h-full overflow-hidden">
                <ProductFilter
                  filterOptions={filterOptions}
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  isMobile={true}
                  onMobileClose={closeMobileFilter}
                />
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1 min-w-0">
            <ProductGrid
              products={sortedProducts}
              sortBy={sortBy}
              onSortChange={setSortBy}
            />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
