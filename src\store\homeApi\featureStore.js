import { create } from 'zustand';
import apiClient from '../../api/axiosConfig.js';

export const useFeatureStore = create((set) => ({
  // State
  featureData: [],
  isLoading: false,
  error: null,

  // Actions
  fetchFeatureData: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Now just use the endpoint path, base URL is already configured
      const response = await apiClient.get('/api/home-product');
      console.log(response)
      
      // Extract the data array from the response
      const data = response.data.data || [];
      
      set({ 
        featureData: data,
        isLoading: false,
        error: null 
      });
      
      return data;
    } catch (error) {
      console.error('Error fetching feature data:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch feature data';
      
      set({ 
        featureData: [],
        isLoading: false,
        error: errorMessage 
      });
      throw error;
    }
  },

  // Clear error
  clearError: () => set({ error: null }),

  // Reset store
  reset: () => set({ 
    featureData: [],
    isLoading: false,
    error: null 
  }),
}));
