import { useState, useEffect} from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Footer from "../layout/footer";

import product1Img from "../assets/images/factoryImg.jpg";

const allProducts = [
  {
    id: 1,
    title: "Industrial Air Compressor",
    description: "High-performance compressor for large-scale operations.",
    image: product1Img,
    tags: ["Airpump"],
    price: 1200,
    capacity: "80 Gallon",
    power: "5 HP",
    weight: "350 lbs",
  },
  {
    id: 2,
    title: "Portable Air Compressor",
    description: "Compact and powerful solution for mobile use.",
    image: product1Img,
    tags: ["Airpump"],
    price: 450,
    capacity: "20 Gallon",
    power: "2 HP",
    weight: "75 lbs",
  },
  {
    id: 3,
    title: "Commercial Air Compressor",
    description: "Reliable compressor for shops and small businesses.",
    image: product1Img,
    tags: ["Airpump"],
    price: 800,
    capacity: "60 Gallon",
    power: "3 HP",
    weight: "220 lbs",
  },
  {
    id: 4,
    title: "Heavy-Duty Industrial Compressor",
    description: "Maximum power for industrial applications.",
    image: product1Img,
    tags: ["Airpump"],
    price: 1500,
    capacity: "120 Gallon",
    power: "7.5 HP",
    weight: "450 lbs",
  },
  {
    id: 5,
    title: "Replacement Filter",
    description: "High-quality replacement filter for air compressors.",
    image: product1Img,
    tags: ["Spare Parts"],
    price: 50,
    capacity: "N/A",
    power: "N/A",
    weight: "2 lbs",
  },
  {
    id: 6,
    title: "Pressure Gauge",
    description: "Accurate pressure gauge for monitoring.",
    image: product1Img,
    tags: ["Spare Parts"],
    price: 25,
    capacity: "N/A",
    power: "N/A",
    weight: "1 lb",
  },
  {
    id: 7,
    title: "Air Hose",
    description: "Durable air hose for various applications.",
    image: product1Img,
    tags: ["Spare Parts"],
    price: 75,
    capacity: "50 feet",
    power: "N/A",
    weight: "8.5 lbs",
  },
];

export default function ProductDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);

  useEffect(() => {
    const foundProduct = allProducts.find((p) => p.id === parseInt(id));
    if (foundProduct) {
      setProduct(foundProduct);
    } else {
      navigate("/products");
    }
  }, [id, navigate]);

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-[var(--primary)] border-t-transparent mx-auto"></div>
          <p className="mt-4 text-gray-700 font-semibold text-base">
            Loading product details...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className=" min-h-screen flex flex-col">
      <main className="flex-grow max-w-7xl mx-auto w-full">
        {/* Main product content container */}
        <div className="px-6 md:px-20 py-16">
          {/* Back Button */}
          <button
            onClick={() => navigate(-1)}
            className="inline-flex items-center gap-2 text-[var(--primary)] font-semibold hover:underline focus:outline-none focus:ring-2 focus:ring-[var(--primary)] rounded mb-10 select-none transition"
            aria-label="Back to products"
          >
            <ArrowLeft size={22} />
            Back to Products
          </button>

          {/* Product Details */}
          <div className="lg:flex gap-16">
            {/* Image Container */}
            <div className="flex-shrink-0 rounded-xl shadow-lg border border-gray-200 overflow-hidden lg:h-auto lg:flex-1">
              <img
                src={product.image}
                alt={product.title}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>

            {/* Content Container */}
            <section className="flex-1 flex flex-col justify-between mt-10 lg:mt-0">
              <div>
                <h1 className="text-4xl font-extrabold text-gray-900 mb-4 leading-tight">
                  {product.title}
                </h1>
                <p className="text-gray-700 text-lg mb-8 leading-relaxed">
                  {product.description}
                </p>

                <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <h2 className="text-xl font-semibold text-[var(--primary)] mb-5 border-b-2 border-[var(--primary)] pb-2">
                    Specifications
                  </h2>

                  <dl className="divide-y divide-gray-100">
                    {[
                      { label: "Power", value: product.power },
                      { label: "Weight", value: product.weight },
                      { label: "Capacity", value: product.capacity },
                    ].map(({ label, value }) => (
                      <div
                        key={label}
                        className="flex justify-between py-3 group transition-colors hover:bg-gray-50 rounded"
                      >
                        <dt className="font-medium text-gray-600 uppercase tracking-wide text-sm">
                          {label}:
                        </dt>
                        <dd className="text-gray-900 font-semibold text-lg">
                          {value}
                        </dd>
                      </div>
                    ))}

                    {/* Price Row */}
                    <div className="flex justify-between py-4 pt-6 mt-6 border-t-2 border-[var(--primary)] rounded">
                      <dt className="font-extrabold text-[var(--primary)] uppercase tracking-wide text-lg">
                        Price:
                      </dt>
                      <dd className="text-[var(--primary)] font-extrabold text-2xl select-none">
                        ${product.price}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            </section>
          </div>
        </div>

        {/* Features Section */}
        <section className=" border-t border-gray-200 px-6 md:px-20 py-20">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-bold text-[var(--primary)] mb-8 border-b-4 border-[var(--primary)] w-1/2 pb-2">
              Features
            </h2>

            <ul className="grid grid-cols-1 sm:grid-cols-2 gap-8">
              {(
                product.features || [
                  "Robust steel construction",
                  "Energy efficient motor",
                  "Low noise operation",
                  "Easy maintenance design",
                ]
              ).map((feature, idx) => (
                <li
                  key={idx}
                  className="group bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition duration-300 hover:border-[var(--primary)] list-none"
                >
                  <article className="flex items-start gap-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[var(--primary)] text-white font-bold text-sm shrink-0 shadow-md group-hover:scale-105 transition-transform">
                      0{idx + 1}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-1">
                        {feature}
                      </h3>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        This feature provides enhanced performance, reliability,
                        and user convenience in industrial environments.
                      </p>
                    </div>
                  </article>
                </li>
              ))}
            </ul>
          </div>
        </section>

        {/* Working Principle Section */}
        <section className="bg-white border-t border-gray-200 px-6 md:px-20 py-14">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-[var(--primary)] mb-6 border-b-4 border-[var(--primary)] inline-block pb-1">
              Working Principle
            </h2>
            <p className="text-gray-700 text-lg leading-relaxed">
              {product.workingPrinciple ||
                "This compressor uses a piston mechanism to compress air in multiple stages, ensuring consistent high pressure and volume output suitable for heavy-duty industrial applications."}
            </p>
          </div>
        </section>

        {/* Technical Specifications Table */}
        <section className="bg-white border-t border-gray-200 px-6 md:px-20 py-20 overflow-x-auto">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-bold text-[var(--primary)] w-1/2 mb-6 border-b-4 border-[var(--primary)] pb-2">
              Techspecs
            </h2>
            <p className="text-xl font-medium text-gray-800 mb-10 text-center">
              Main Technical Parameters
            </p>

            <table className="w-full text-sm text-left border-collapse border border-gray-300 shadow-md">
              <thead className="bg-gray-800 text-white text-[15px]">
                <tr>
                  <th className="px-4 py-3 border border-gray-300">Model</th>
                  <th className="px-4 py-3 border border-gray-300">
                    Rotate Speed (r/min)
                  </th>
                  <th className="px-4 py-3 border border-gray-300">
                    Ball Weight (t)
                  </th>
                  <th className="px-4 py-3 border border-gray-300">
                    Feedstock Size (mm)
                  </th>
                  <th className="px-4 py-3 border border-gray-300">
                    Discharge Size (mm)
                  </th>
                  <th className="px-4 py-3 border border-gray-300">
                    Capacity (t/h)
                  </th>
                  <th className="px-4 py-3 border border-gray-300">
                    Motor Power (kw)
                  </th>
                  <th className="px-4 py-3 border border-gray-300">
                    Weight (t)
                  </th>
                </tr>
              </thead>
              <tbody className="text-gray-800 text-[14px]">
                {[
                  [
                    "φ900×1800",
                    "36–38",
                    "1.5",
                    "≤20",
                    "0.075–0.89",
                    "0.65–2",
                    "18.5",
                    "4.6",
                  ],
                  [
                    "φ900×3000",
                    "36",
                    "2.7",
                    "≤20",
                    "0.075–0.89",
                    "1.1–3.5",
                    "22",
                    "5.6",
                  ],
                  [
                    "φ1200×2400",
                    "36",
                    "3",
                    "≤25",
                    "0.075–0.6",
                    "1.5–4.8",
                    "30",
                    "12",
                  ],
                  [
                    "φ1200×3000",
                    "36",
                    "3.5",
                    "≤25",
                    "0.074–0.4",
                    "1.6–5",
                    "37",
                    "12.8",
                  ],
                  [
                    "φ1200×4500",
                    "32.4",
                    "5",
                    "≤25",
                    "0.074–0.4",
                    "1.6–5.8",
                    "55",
                    "13.8",
                  ],
                  [
                    "φ1500×3000",
                    "29.7",
                    "7.5",
                    "≤25",
                    "0.074–0.4",
                    "2–5",
                    "75",
                    "15.6",
                  ],
                  [
                    "φ1500×4500",
                    "27",
                    "11",
                    "≤25",
                    "0.074–0.4",
                    "3–6",
                    "90",
                    "21",
                  ],
                  [
                    "φ1500×5700",
                    "28",
                    "12",
                    "≤25",
                    "0.074–0.4",
                    "3.5–6",
                    "130",
                    "24.7",
                  ],
                  [
                    "φ1830×3000",
                    "25.4",
                    "11",
                    "≤25",
                    "0.074–0.4",
                    "4–10",
                    "130",
                    "28",
                  ],
                  [
                    "φ1830×4500",
                    "25.4",
                    "15",
                    "≤25",
                    "0.074–0.4",
                    "4.5–12",
                    "155",
                    "32",
                  ],
                  [
                    "φ1830×6400",
                    "24.1",
                    "21",
                    "≤25",
                    "0.074–0.4",
                    "6.5–15",
                    "180",
                    "34",
                  ],
                  [
                    "φ1830×7000",
                    "24.1",
                    "23",
                    "≤25",
                    "0.074–0.4",
                    "7.5–17",
                    "210",
                    "36",
                  ],
                ].map((row, rowIndex) => (
                  <tr
                    key={rowIndex}
                    className={rowIndex % 2 === 0 ? "bg-gray-200" : "bg-white"}
                  >
                    {row.map((cell, colIndex) => (
                      <td
                        key={colIndex}
                        className="px-4 py-3 border border-gray-300 whitespace-nowrap"
                      >
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
