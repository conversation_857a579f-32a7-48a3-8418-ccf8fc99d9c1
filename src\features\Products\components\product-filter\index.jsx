// src/components/products/product-filter/ProductFilter.jsx
import { Check, DollarSign, Wrench, RotateCcw } from "lucide-react";

export default function ProductFilter({ filterOptions, filters, onFilterChange, isMobile = false, onMobileClose }) {
  const handleFilterClick = (filterType, value) => {
    onFilterChange(filterType, value);
    // Close mobile filter when a selection is made (optional)
    if (isMobile && onMobileClose) {
      setTimeout(() => onMobileClose(), 300);
    }
  };

  const clearAllFilters = () => {
    onFilterChange('productType', 'All');
    onFilterChange('priceRange', 'All Prices');
  };

  const hasActiveFilters = filters.productType !== 'All' || filters.priceRange !== 'All Prices';

  // Define product types
  const productTypes = ['All', 'Airpump', 'Spare Parts'];

  if (isMobile) {
    return (
      <div className="space-y-6 h-full flex flex-col">
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Product Type Section */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide sticky top-0 bg-white py-2 flex items-center gap-2">
              <Wrench size={16} />
              Product Type
            </h4>
            <div className="space-y-2">
              {productTypes.map((type) => (
                <button
                  key={type}
                  onClick={() => handleFilterClick('productType', type)}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm transition-all ${
                    filters.productType === type
                      ? "bg-[var(--primary)] text-white shadow-md"
                      : "text-gray-700 hover:bg-gray-100 hover:text-[var(--primary)]"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{type}</span>
                    {filters.productType === type && <Check size={16} />}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Price Range Section */}
          <div className="border-t pt-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide flex items-center gap-2 sticky top-0 bg-white py-2">
              <DollarSign size={16} />
              Price Range
            </h4>
            <div className="space-y-2">
              {filterOptions.priceRanges.map((priceRange) => (
                <button
                  key={priceRange.label}
                  onClick={() => handleFilterClick('priceRange', priceRange.label)}
                  className={`w-full text-left px-4 py-3 rounded-lg text-sm transition-all ${
                    filters.priceRange === priceRange.label
                      ? "bg-[var(--primary)] text-white shadow-md"
                      : "text-gray-700 hover:bg-gray-100 hover:text-[var(--primary)]"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{priceRange.label}</span>
                    {filters.priceRange === priceRange.label && <Check size={16} />}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Fixed Footer with Clear Button */}
        {hasActiveFilters && (
          <div className="p-4 border-t bg-gray-50 mt-auto">
            <button
              onClick={clearAllFilters}
              className="w-full px-4 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-all flex items-center justify-center gap-2"
            >
              <RotateCcw size={16} />
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    );
  }

  // Desktop version - Using CSS variables for navbar height
  return (
    <div 
      className="bg-white rounded-xl shadow-md overflow-hidden sticky"
      style={{ 
        top: 'calc(var(--navbar-height) + 1.5rem)',
        maxHeight: 'calc(100vh - var(--navbar-height) - 3rem)'
      }}
    >
      {/* Fixed Header */}
      <div className="p-6 border-b bg-white">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-[#1c1a30] flex items-center gap-2">
            <Wrench size={20} />
            Filters
          </h3>
        </div>
      </div>

      {/* Scrollable Content */}
      <div 
        className="overflow-y-auto" 
        style={{ maxHeight: 'calc(100vh - var(--navbar-height) - 12rem)' }}
      >
        <div className="p-6 space-y-6">
          {/* Product Type Section */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide flex items-center gap-2">
              <Wrench size={14} />
              Product Type
            </h4>
            <div className="space-y-1">
              {productTypes.map((type) => (
                <button
                  key={type}
                  onClick={() => handleFilterClick('productType', type)}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all ${
                    filters.productType === type
                      ? "bg-[var(--primary)] text-white shadow-md"
                      : "text-gray-700 hover:bg-gray-100 hover:text-[var(--primary)]"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{type}</span>
                    {filters.productType === type && <Check size={14} />}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Price Range Section */}
          <div className="border-t pt-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3 uppercase tracking-wide flex items-center gap-2">
              <DollarSign size={14} />
              Price Range
            </h4>
            <div className="space-y-1">
              {filterOptions.priceRanges.map((priceRange) => (
                <button
                  key={priceRange.label}
                  onClick={() => handleFilterClick('priceRange', priceRange.label)}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all ${
                    filters.priceRange === priceRange.label
                      ? "bg-[var(--primary)] text-white shadow-md"
                      : "text-gray-700 hover:bg-gray-100 hover:text-[var(--primary)]"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{priceRange.label}</span>
                    {filters.priceRange === priceRange.label && <Check size={14} />}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Footer with Clear Button */}
      {hasActiveFilters && (
        <div className="p-4 border-t bg-gray-50">
          <button
            onClick={clearAllFilters}
            className="w-full px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-white hover:border-red-300 hover:text-red-600 transition-all flex items-center justify-center gap-2"
          >
            <RotateCcw size={14} />
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  );
}
