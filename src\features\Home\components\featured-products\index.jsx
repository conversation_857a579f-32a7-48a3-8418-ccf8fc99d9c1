import bgImage from "../../../../assets/images/factoryImg.jpg";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import { useFeatureStore } from "../../../../store/homeApi/featureStore";
import { useEffect, useState } from "react";

// Updated ProductImage component (cache-aware, native img)
const ProductImage = ({ src, alt }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (!src) {
      setImageError(true);
      setImageLoaded(true);
      return;
    }

    const img = new Image();
    img.src = src;

    if (img.complete) {
      setImageLoaded(true);
    } else {
      img.onload = () => setImageLoaded(true);
      img.onerror = () => {
        setImageError(true);
        setImageLoaded(true);
      };
    }
  }, [src]);

  return (
    <div className="relative w-full h-40 sm:h-44 md:h-48 overflow-hidden">
      {!imageLoaded && (
        <div className="absolute inset-0 bg-gray-300 animate-pulse z-10"></div>
      )}
      {imageLoaded && (
        <img
          src={imageError ? bgImage : src}
          alt={alt}
          className="w-full h-full object-cover transition-all duration-300 hover:scale-105"
          loading="lazy"
        />
      )}
    </div>
  );
};

// Skeleton component for loading state
const ProductSkeleton = () => (
  <div className="bg-white text-black rounded-xl shadow-md overflow-hidden h-full animate-pulse">
    <div className="w-full h-40 sm:h-44 md:h-48 bg-gray-300"></div>
    <div className="p-4 sm:p-5 md:p-6 bg-white">
      <div className="h-6 bg-gray-300 rounded mb-3"></div>
      <div className="space-y-2 mb-4">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
      <div className="h-4 bg-gray-300 rounded w-20"></div>
    </div>
  </div>
);

// Error component
const ErrorState = ({ error, onRetry }) => (
  <div className="relative z-10 -mt-38 pb-15">
    <div className="max-w-xs md:max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
      <div className="bg-white rounded-xl shadow-md p-8 text-center">
        <div className="text-red-500 mb-4">
          <svg
            className="w-16 h-16 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Failed to load featured products
        </h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={onRetry}
          className="bg-[var(--primary)] text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity"
        >
          Try Again
        </button>
      </div>
    </div>
  </div>
);

// Loading skeleton for the carousel
const LoadingSkeleton = () => (
  <div className="relative z-10 -mt-38 pb-15">
    <div className="max-w-xs md:max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {[...Array(4)].map((_, index) => (
          <ProductSkeleton key={index} />
        ))}
      </div>
    </div>
  </div>
);

export default function FeaturedProducts() {
  const { featureData, isLoading, error, fetchFeatureData } = useFeatureStore();

  useEffect(() => {
    fetchFeatureData();
  }, [fetchFeatureData]);

  const hasEnoughSlidesForLoop = featureData && featureData.length > 4;
  const displayData = featureData || [];

  return (
    <section className="relative z-0">
      {/* Top Section with Overlay */}
      <div
        className="relative pt-12 pb-50 sm:pt-16 md:pt-20 px-4 sm:px-6 md:px-12 lg:px-20 text-white"
        style={{
          backgroundImage: `linear-gradient(rgba(28, 26, 48, 0.85), rgba(28, 26, 48, 0.85)), url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 lg:gap-8">
            <div className="flex-1">
              <p className="text-xs sm:text-sm uppercase text-red-500 tracking-wider font-medium mb-2">
                Featured Products
              </p>
              <h2 className="text-2xl sm:text-3xl md:text-3xl lg:text-4xl font-bold leading-tight">
                High Quality Mining Equipment <br className="hidden sm:block" />{" "}
                For Many Applications.
              </h2>
            </div>
            <div className="flex-1 lg:max-w-2xl">
              <p className="text-sm sm:text-base md:text-md text-gray-300 leading-relaxed">
                Professional mining equipment for stone crushing, mining
                separating, ore beneficiation, drying, briquette making, sand
                making and more.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && <LoadingSkeleton />}

      {/* Error State */}
      {error && !isLoading && (
        <ErrorState error={error} onRetry={fetchFeatureData} />
      )}

      {/* Success State - Cards Section */}
      {!isLoading && !error && displayData.length > 0 && (
        <div className="relative z-10 -mt-38 pb-15">
          <div className="max-w-xs md:max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
            <Swiper
              modules={[Pagination, Autoplay]}
              spaceBetween={16}
              loop={hasEnoughSlidesForLoop}
              autoplay={
                hasEnoughSlidesForLoop
                  ? {
                      delay: 3000,
                      disableOnInteraction: false,
                      pauseOnMouseEnter: false,
                    }
                  : false
              }
              pagination={{
                clickable: true,
                dynamicBullets: false,
              }}
              breakpoints={{
                320: {
                  slidesPerView: 1,
                  spaceBetween: 16,
                },
                768: {
                  slidesPerView: Math.min(2, displayData.length),
                  spaceBetween: 20,
                },
                1024: {
                  slidesPerView: Math.min(4, displayData.length),
                  spaceBetween: 24,
                },
              }}
              className="w-full products-swiper"
            >
              {displayData.map((product) => (
                <SwiperSlide key={product.id || product.slug}>
                  <div className="bg-white text-black rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 h-full">
                    <ProductImage src={product.image} alt={product.name} />
                    <div className="p-4 sm:p-5 md:p-6 bg-white">
                      <h4 className="font-semibold text-base sm:text-lg md:text-xl mb-2 sm:mb-3 min-h-[3rem] sm:min-h-[3.5rem] md:min-h-[4rem] line-clamp-2">
                        {product.name}
                      </h4>
                      <p className="text-xs sm:text-sm md:text-base text-gray-600 leading-relaxed min-h-[4rem] sm:min-h-[4.5rem] md:min-h-[5rem] line-clamp-3">
                        {product.summary}
                      </p>
                      <a
                        href={product.read_more || "#"}
                        className="text-xs sm:text-sm md:text-base text-[var(--primary)] font-medium mt-3 sm:mt-4 inline-block hover:underline transition-colors duration-200"
                        target={product.read_more ? "_blank" : "_self"}
                        rel={product.read_more ? "noopener noreferrer" : ""}
                      >
                        Read More
                      </a>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && displayData.length === 0 && (
        <div className="relative z-10 -mt-38 pb-15">
          <div className="max-w-xs md:max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
            <div className="bg-white rounded-xl shadow-md p-8 text-center">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-16 h-16 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0L9 7v4"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Featured Products
              </h3>
              <p className="text-gray-600">
                No featured products are currently available.
              </p>
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
