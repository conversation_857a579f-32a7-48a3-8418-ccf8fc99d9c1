import { useEffect, useState } from "react";
import { useNewsStore } from "../../../../store/homeApi/newsStore";
import { Calendar, ExternalLink } from "lucide-react";
import bgImage from "../../../../assets/images/factoryImg.jpg"; // fallback image

// Image component with loading + error fallback
const NewsImage = ({ src, alt }) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!src) {
      setError(true);
      setLoaded(true);
      return;
    }
    const img = new Image();
    img.src = src;
    if (img.complete) {
      setLoaded(true);
    } else {
      img.onload = () => setLoaded(true);
      img.onerror = () => {
        setError(true);
        setLoaded(true);
      };
    }
  }, [src]);

  return (
    <div className="w-full h-48 sm:h-56 lg:h-48 relative overflow-hidden">
      {!loaded && (
        <div className="absolute inset-0 bg-gray-300 animate-pulse z-10"></div>
      )}
      {loaded && (
        <img
          src={error ? bgImage : src}
          alt={alt}
          className="w-full h-full object-cover transition-transform duration-300 ease-out group-hover:scale-105"
          loading="lazy"
        />
      )}
    </div>
  );
};

export default function LatestNews() {
  const { fetchNewsData, newsData, error, loading } = useNewsStore();

  useEffect(() => {
    fetchNewsData();
  }, [fetchNewsData]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="animate-pulse">Loading latest news...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center text-red-600">
          Error loading news: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-100 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center lg:text-left mb-8">
          <p className="font-semibold text-sm sm:text-base lg:text-xl uppercase tracking-wide mb-3 sm:mb-4 text-[var(--primary)]">
            Latest Industry News
          </p>
          <h1 className="text-lg text-black sm:text-2xl md:text-2xl lg:text-3xl font-bold py-4 leading-tight text-balance lg:text-pretty">
            Stay Updated With The Latest Mining Equipment News & Insights
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {newsData.map((newsItem, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-xl overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 ease-out max-w-md mx-auto lg:max-w-none group cursor-pointer"
            >
              <div className="relative overflow-hidden">
                {/* Overlay for shadow effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-100 group-hover:opacity-60 transition-opacity duration-300 z-10"></div>

                {/* Replaced LazyLoadImage with NewsImage */}
                <NewsImage src={newsItem.image} alt={newsItem.title} />
              </div>

              <div className="p-6 sm:p-8 group-hover:bg-gray-50 transition-colors duration-300">
                <div className="mb-4">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-3 leading-tight line-clamp-2 group-hover:text-[var(--primary)] transition-colors duration-300">
                    {newsItem.title}
                  </h2>
                  <p className="text-gray-600 text-sm sm:text-base leading-relaxed line-clamp-3 mb-4 group-hover:text-gray-700 transition-colors duration-300">
                    {newsItem.summary}
                  </p>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-100 group-hover:border-gray-200 transition-colors duration-300">
                  <div className="flex items-center gap-2 text-gray-500 text-sm group-hover:text-gray-600 transition-colors duration-300">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(newsItem.posting_date)}</span>
                  </div>

                  <a
                    href={newsItem.read_more}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-sm font-semibold text-[var(--primary)] hover:opacity-80 hover:underline transition-all duration-300 transform hover:translate-x-1"
                  >
                    Read More
                    <ExternalLink className="w-4 h-4 transition-transform duration-300 group-hover:rotate-12" />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {newsData.length === 0 && (
          <div className="text-center py-12">
            <div className="bg-white rounded-xl shadow-xl p-8 sm:p-12 max-w-md mx-auto hover:shadow-2xl transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                No News Available
              </h3>
              <p className="text-gray-600">
                Check back later for the latest industry updates.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
